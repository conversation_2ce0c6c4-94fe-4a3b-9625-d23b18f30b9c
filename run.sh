#!/bin/bash

# ---- CONFIG ----
TEST_CLIP="test.mp4"
OUTPUT_DIR="rife-test"
RIFE_PATH="/mnt/c/Users/<USER>/code/Practical-RIFE"  # WSL2 path to Windows directory
TARGET_FPS=60
# ----------------

set -e  # Exit on error
set -o pipefail  # Exit on pipe failures

# ---- Command line argument parsing ----
if [ "$1" = "--test-one" ] || [ "$1" = "-t" ]; then
  echo "🧪 Test mode: Running only one RIFE test with full debugging"
  TEST_MODE=true
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  echo "Usage: $0 [OPTIONS]"
  echo ""
  echo "Description:"
  echo "  Tests multiple RIFE interpolation methods on test.mp4 (Windows/CUDA optimized)"
  echo ""
  echo "Options:"
  echo "  --test-one, -t           Run only first test with full debugging output"
  echo "  --help, -h               Show this help message"
  echo ""
  echo "Configuration:"
  echo "  Input: $TEST_CLIP"
  echo "  Output directory: $OUTPUT_DIR/"
  echo "  Target FPS: $TARGET_FPS"
  echo "  Max parallel tests: $MAX_PARALLEL"
  echo "  GPU: NVIDIA CUDA acceleration"
  exit 0
fi

echo "🎬 RIFE Interpolation Testing Script (Windows/CUDA)"
echo "=================================================="

# ---- Check input file ----
if [ ! -f "$TEST_CLIP" ]; then
  echo "❌ ERROR: Test clip does not exist: $TEST_CLIP"
  echo "Please make sure test.mp4 exists in the current directory."
  exit 1
fi

# ---- Check RIFE_PATH ----
if [ ! -d "$RIFE_PATH" ]; then
  echo "❌ ERROR: RIFE_PATH directory does not exist: $RIFE_PATH"
  echo "Please update RIFE_PATH to point to your Practical-RIFE installation"
  exit 1
fi

if [ ! -f "$RIFE_PATH/inference_video.py" ]; then
  echo "❌ ERROR: inference_video.py not found in RIFE_PATH: $RIFE_PATH"
  exit 1
fi

echo "✅ Test clip and RIFE installation found"

# ---- Setup ----
CURRENT_DIR=$(pwd)
mkdir -p "$OUTPUT_DIR"
MEASUREMENTS_FILE="$OUTPUT_DIR/measurements.txt"
# Sequential execution - no parallel arrays needed

# ---- Check NVIDIA GPU and CUDA ----
echo "🔍 Checking NVIDIA GPU and CUDA support..."
echo "   Current directory: $(pwd)"
echo "   RIFE path: $RIFE_PATH"
cd "$RIFE_PATH" || { echo "❌ Failed to change to RIFE directory"; exit 1; }
echo "   Changed to RIFE directory: $(pwd)"

# Check if CUDA is available (using Windows Python)
cuda_available=$(python.exe -c "import torch; print('yes' if torch.cuda.is_available() else 'no')" 2>/dev/null || echo "no")
if [ "$cuda_available" = "yes" ]; then
  gpu_name=$(python.exe -c "import torch; print(torch.cuda.get_device_name(0))" 2>/dev/null || echo "Unknown")
  gpu_memory=$(python.exe -c "import torch; print(f'{torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB')" 2>/dev/null || echo "Unknown")
  echo "✅ CUDA GPU acceleration available"
  echo "   GPU: $gpu_name"
  echo "   VRAM: $gpu_memory"
else
  echo "⚠️  CUDA not available - will use CPU only"
  echo "   Make sure NVIDIA drivers and CUDA toolkit are installed"
fi
cd "$CURRENT_DIR" || { echo "❌ Failed to return to original directory"; exit 1; }
echo "   Returned to: $(pwd)"

# Initialize measurements file
echo "RIFE Performance Measurements (Windows/CUDA)" > "$MEASUREMENTS_FILE"
echo "=============================================" >> "$MEASUREMENTS_FILE"
echo "Test file: $TEST_CLIP" >> "$MEASUREMENTS_FILE"
echo "Target FPS: $TARGET_FPS" >> "$MEASUREMENTS_FILE"
echo "Max parallel tests: $MAX_PARALLEL" >> "$MEASUREMENTS_FILE"
echo "GPU: $gpu_name ($gpu_memory)" >> "$MEASUREMENTS_FILE"
echo "CUDA available: $cuda_available" >> "$MEASUREMENTS_FILE"
echo "Test date: $(date)" >> "$MEASUREMENTS_FILE"
echo "" >> "$MEASUREMENTS_FILE"

# Get original FPS and duration for reference
echo "🔍 Analyzing input video..."
original_fps=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate -of csv=p=0 "$TEST_CLIP" | bc -l | xargs printf "%.2f")
test_duration=$(ffprobe -v quiet -show_entries format=duration -of csv=p=0 "$TEST_CLIP" | xargs printf "%.2f")
echo "   Input: $TEST_CLIP"
echo "   Original FPS: $original_fps"
echo "   Target FPS: $TARGET_FPS"
echo "   Test duration: ${test_duration}s"

# Add to measurements file
echo "Input video specs:" >> "$MEASUREMENTS_FILE"
echo "- Duration: ${test_duration} seconds" >> "$MEASUREMENTS_FILE"
echo "- Original FPS: $original_fps" >> "$MEASUREMENTS_FILE"
echo "- Target FPS: $TARGET_FPS" >> "$MEASUREMENTS_FILE"
echo "" >> "$MEASUREMENTS_FILE"

echo ""
echo "🤖 Testing RIFE interpolation methods"
echo "============================================"
echo "🔍 Preparing test configurations..."

# Define test configurations optimized for NVIDIA RTX 4090
# Each test has: name, exp_value, scale, additional_args
declare -a TESTS=(
  "cuda_fast_2x:2:2.0:"
  "cuda_standard_2x:2:1.0:"
  "cuda_hd_2x:2:0.5:"
  "cuda_fp16_2x:2:1.0:--fp16"
  "cuda_uhd_2x:2:0.25:--UHD"
  "cuda_uhd_fp16:2:0.25:--UHD --fp16"
  "cuda_4x:4:1.0:"
  "cuda_4x_fp16:4:1.0:--fp16"
  "cpu_baseline:2:1.0:"
  "cuda_experimental:2:1.5:--fp16"
)

# Function to run a single RIFE test
run_single_test() {
  local test_config="$1"
  local test_index="$2"
  local total_tests="$3"

  # Parse test configuration
  IFS=':' read -r test_name exp_val scale_val extra_args <<< "$test_config"

  # Create log file for this test
  local test_log="$OUTPUT_DIR/test_${test_name}.log"

  # Redirect all output to log file for this process
  exec > >(tee "$test_log") 2>&1

  echo "🧪 Test $test_index/$total_tests: $test_name"
  echo "   EXP: $exp_val, Scale: $scale_val, Extra: $extra_args"
  echo ""

  # Start timing
  test_start_time=$(date +%s)

  # Create temporary directories for this test
  local temp_frames="$OUTPUT_DIR/temp_frames_$test_name"
  local temp_interpolated="$OUTPUT_DIR/temp_interpolated_$test_name"
  local output_file="$OUTPUT_DIR/${test_name}_${TARGET_FPS}fps.mp4"

  echo "   ⏳ Step 1/4: Extracting frames from video..."
  mkdir -p "$temp_frames"
  ffmpeg -y -hide_banner -loglevel error -i "$TEST_CLIP" "$temp_frames/%06d.png" || {
    echo "   ❌ Failed to extract frames for $test_name"
    return 1
  }

  frame_count=$(find "$temp_frames" -name "*.png" -type f | wc -l)
  echo "   ✅ Extracted $frame_count frames"
  echo ""

  echo "   ⏳ Step 2/4: Running RIFE interpolation (this may take several minutes)..."
  cd "$RIFE_PATH"

  # Set environment variables for CUDA optimization
  export CUDA_VISIBLE_DEVICES=0
  export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:2048,expandable_segments:True

  # Start RIFE timing
  rife_start_time=$(date +%s)

  # Build RIFE command (using Windows Python from WSL2)
  # Convert WSL2 paths to Windows paths for python.exe
  win_input_path=$(echo "$CURRENT_DIR/$temp_frames" | sed 's|/mnt/c|C:|' | sed 's|/|\\|g')

  # RIFE always outputs to its default vid_out directory, so we'll move files afterward
  # Clear any existing files in RIFE's vid_out directory first
  rife_vid_out="$RIFE_PATH/vid_out"
  if [ -d "$rife_vid_out" ]; then
    rm -f "$rife_vid_out"/*.png 2>/dev/null
  fi

  # Create our target output directory
  mkdir -p "$CURRENT_DIR/$temp_interpolated"

  rife_cmd="python.exe inference_video.py --img \"$win_input_path\" --exp $exp_val"

  if [ -n "$scale_val" ] && [ "$scale_val" != "1.0" ]; then
    rife_cmd="$rife_cmd --scale $scale_val"
  fi

  if [ -n "$extra_args" ]; then
    rife_cmd="$rife_cmd $extra_args"
  fi

  echo "   💻 Command: $rife_cmd"
  echo "   📂 Working directory: $(pwd)"
  echo "   📁 Input frames: $CURRENT_DIR/$temp_frames"
  echo "   📁 RIFE will output to: $rife_vid_out (then moved to $CURRENT_DIR/$temp_interpolated)"

  # Create log file for this test
  log_file="$CURRENT_DIR/$OUTPUT_DIR/rife_log_$test_name.txt"

  # Check if timeout is available (gtimeout on WSL)
  if command -v timeout >/dev/null 2>&1; then
    timeout_cmd="timeout 600"  # 10 minutes for CUDA
  elif command -v gtimeout >/dev/null 2>&1; then
    timeout_cmd="gtimeout 600"
  else
    timeout_cmd=""
    echo "   ⚠️  No timeout available - running without time limit"
  fi

  # Run RIFE with timeout and capture output
  echo "   ⏱️  Running with 10-minute timeout..."

  if [ "$TEST_MODE" = true ]; then
    echo "   🔍 DEBUG MODE: Running with full output..."
    if [ -n "$timeout_cmd" ]; then
      if $timeout_cmd bash -c "$rife_cmd"; then
        echo "   ✅ RIFE command completed successfully"
      else
        echo "   ❌ RIFE command failed with exit code: $?"
        cd "$CURRENT_DIR"
        return 1
      fi
    else
      if bash -c "$rife_cmd"; then
        echo "   ✅ RIFE command completed successfully"
      else
        echo "   ❌ RIFE command failed with exit code: $?"
        cd "$CURRENT_DIR"
        return 1
      fi
    fi
  else
    if [ -n "$timeout_cmd" ]; then
      if $timeout_cmd bash -c "$rife_cmd" > "$log_file" 2>&1; then
        echo "   ✅ RIFE command completed successfully"
      else
        echo "   ❌ RIFE interpolation failed or timed out for $test_name"
        echo "   📋 Check log file: $log_file"
        echo "   🔍 Last 10 lines of error log:"
        tail -10 "$log_file" 2>/dev/null | sed 's/^/      /' || echo "      (No log file found)"
        cd "$CURRENT_DIR"
        return 1
      fi
    else
      if bash -c "$rife_cmd" > "$log_file" 2>&1; then
        echo "   ✅ RIFE command completed successfully"
      else
        echo "   ❌ RIFE interpolation failed for $test_name"
        echo "   📋 Check log file: $log_file"
        echo "   🔍 Last 10 lines of error log:"
        tail -10 "$log_file" 2>/dev/null | sed 's/^/      /' || echo "      (No log file found)"
        cd "$CURRENT_DIR"
        return 1
      fi
    fi
  fi

  cd "$CURRENT_DIR"

  # End RIFE timing
  rife_end_time=$(date +%s)
  rife_duration=$((rife_end_time - rife_start_time))

  # Check if RIFE succeeded by looking for interpolated frames (check both expected location and default vid_out)
  rife_vid_out="$RIFE_PATH/vid_out"
  interpolated_count=$(find "$temp_interpolated" -name "*.png" -type f 2>/dev/null | wc -l)

  # If no frames in expected location, check RIFE's default vid_out directory
  if [ "$interpolated_count" -eq 0 ] && [ -d "$rife_vid_out" ] && [ "$(ls -A "$rife_vid_out" 2>/dev/null)" ]; then
    echo "   📁 Moving frames from RIFE default output to project directory..."
    mkdir -p "$temp_interpolated"

    # Use PowerShell to copy files from Windows path to Windows path, then delete originals
    win_vid_out=$(echo "$rife_vid_out" | sed 's|/mnt/c|C:|' | sed 's|/|\\|g')
    win_temp_interpolated=$(echo "$temp_interpolated" | sed 's|/mnt/c|C:|' | sed 's|/|\\|g')

    powershell.exe -Command "Copy-Item '$win_vid_out\\*.png' '$win_temp_interpolated\\' -Force"
    powershell.exe -Command "Remove-Item '$win_vid_out\\*.png' -Force"
    interpolated_count=$(find "$temp_interpolated" -name "*.png" -type f 2>/dev/null | wc -l)
    echo "   📦 Moved $interpolated_count frames from vid_out"
  fi

  if [ "$interpolated_count" -gt 0 ]; then
    echo "   ✅ RIFE completed: $frame_count → $interpolated_count frames (${rife_duration}s)"
    echo ""

    # Calculate the correct input framerate to maintain original video duration at 60 FPS output
    # input_fps = interpolated_frames / original_duration
    # This ensures the output video has the same duration as the original but at 60 FPS
    interpolation_ratio=$(echo "scale=6; $interpolated_count / $frame_count" | bc -l)
    input_fps=$(echo "scale=6; $interpolated_count / $test_duration" | bc -l)
    echo "   📊 Interpolation ratio: ${interpolation_ratio}x (${frame_count} → ${interpolated_count} frames)"
    echo "   🎬 Input FPS for encoding: ${input_fps} → Output: ${TARGET_FPS} FPS"
    echo ""

    echo "   ⏳ Step 3/4: Encoding video with NVIDIA hardware acceleration..."
    # Start encoding timing
    encode_start_time=$(date +%s)

    # Try NVENC (NVIDIA hardware encoding) with optimized settings
    if ffmpeg \
      -y \
      -hide_banner \
      -loglevel warning \
      -framerate $input_fps \
      -i "$temp_interpolated/%07d.png" \
      -c:v h264_nvenc \
      -preset p4 \
      -rc vbr \
      -cq 20 \
      -qmin 18 \
      -qmax 22 \
      -r $TARGET_FPS \
      -pix_fmt yuv420p \
      -movflags +faststart \
      "$output_file" 2>/dev/null; then
      echo "   ✅ Encoded with NVENC (hardware acceleration)"
    elif ffmpeg \
      -y \
      -hide_banner \
      -loglevel warning \
      -framerate $input_fps \
      -i "$temp_interpolated/%07d.png" \
      -c:v h264_nvenc \
      -preset fast \
      -b:v 8M \
      -maxrate 12M \
      -bufsize 16M \
      -r $TARGET_FPS \
      -pix_fmt yuv420p \
      -movflags +faststart \
      "$output_file" 2>/dev/null; then
      echo "   ✅ Encoded with NVENC (hardware acceleration - fallback mode)"
    else
      echo "   ⚠️  NVENC failed, using software encoding..."
      ffmpeg \
        -y \
        -hide_banner \
        -loglevel error \
        -framerate $input_fps \
        -i "$temp_interpolated/%07d.png" \
        -c:v libx264 \
        -preset fast \
        -crf 20 \
        -r $TARGET_FPS \
        -pix_fmt yuv420p \
        -movflags +faststart \
        "$output_file" || {
        echo "   ❌ Failed to encode $test_name"
        # rm -rf "$temp_frames" "$temp_interpolated"  # Disabled for debugging
        return 1
      }
    fi

    # End encoding timing
    encode_end_time=$(date +%s)
    encode_duration=$((encode_end_time - encode_start_time))

    # End total test timing
    test_end_time=$(date +%s)
    total_duration=$((test_end_time - test_start_time))

    # Get file size for comparison
    file_size=$(du -h "$output_file" | cut -f1)

    # Calculate processing rate and 2-hour estimate
    processing_rate=$(echo "scale=4; $test_duration / $total_duration" | bc -l)
    two_hour_estimate=$(echo "scale=0; 7200 / $processing_rate" | bc -l)
    two_hour_hours=$(echo "scale=1; $two_hour_estimate / 3600" | bc -l)

    echo "   ✅ Test $test_name completed successfully"
    echo "   📁 Output: $output_file ($file_size)"
    echo "   ⏱️  Total time: ${total_duration}s (RIFE: ${rife_duration}s, Encode: ${encode_duration}s)"
    echo "   📊 Processing rate: ${processing_rate}x realtime"
    echo "   🎬 2-hour video estimate: ${two_hour_hours} hours"

    # Write to measurements file (with file locking for parallel writes)
    (
      flock -x 200
      {
        echo "Test: $test_name"
        echo "- Configuration: EXP=$exp_val, Scale=$scale_val, Args=$extra_args"
        echo "- Frame count: $frame_count → $interpolated_count"
        echo "- RIFE time: ${rife_duration}s"
        echo "- Encoding time: ${encode_duration}s"
        echo "- Total time: ${total_duration}s"
        echo "- Processing rate: ${processing_rate}x realtime"
        echo "- 2-hour video estimate: ${two_hour_hours} hours"
        echo "- Output file size: $file_size"
        echo "- Success: Yes"
        echo ""
      } >> "$MEASUREMENTS_FILE"
    ) 200>>"$MEASUREMENTS_FILE.lock"
  else
    # End total test timing for failed test
    test_end_time=$(date +%s)
    total_duration=$((test_end_time - test_start_time))

    echo "   ❌ RIFE failed to generate interpolated frames for $test_name"
    echo "   ⏱️  Failed after: ${total_duration}s"
    if [ -f "$log_file" ]; then
      echo "   📋 Check log file: $log_file"
      echo "   🔍 Last 10 lines of error log:"
      tail -10 "$log_file" 2>/dev/null | sed 's/^/      /'
    fi

    # Write failed test to measurements file (with file locking)
    (
      flock -x 200
      {
        echo "Test: $test_name"
        echo "- Configuration: EXP=$exp_val, Scale=$scale_val, Args=$extra_args"
        echo "- Total time: ${total_duration}s"
        echo "- Success: No"
        echo "- Error: RIFE failed to generate frames"
        echo ""
      } >> "$MEASUREMENTS_FILE"
    ) 200>>"$MEASUREMENTS_FILE.lock"
  fi

  # Cleanup temporary files
  echo ""
  echo "   ⏳ Step 4/4: Cleaning up temporary files..."
  # rm -rf "$temp_frames" "$temp_interpolated"  # Disabled for debugging

  echo "✅ Test $test_name completed successfully!"
}

# Main sequential execution logic
total_tests=${#TESTS[@]}

# If in test mode, only run the first test
if [ "$TEST_MODE" = true ]; then
  TESTS=("${TESTS[0]}")
  total_tests=1
  echo "🔍 Running only first test: ${TESTS[0]}"
fi

echo ""
echo "🚀 Starting sequential RIFE testing"
echo "============================================================================"
echo "📋 Tests to run: $total_tests"
for i in "${!TESTS[@]}"; do
  IFS=':' read -r test_name _ _ _ <<< "${TESTS[$i]}"
  echo "   $((i+1)). $test_name"
done
echo ""

# Run tests sequentially
test_count=1
for test_config in "${TESTS[@]}"; do
  # Parse test name for display
  IFS=':' read -r test_name _ _ _ <<< "$test_config"

  echo "🚀 Starting test $test_count/$total_tests: $test_name"
  echo "📊 Progress: [$test_count/$total_tests] $(( (test_count-1) * 100 / total_tests ))% complete"

  # Show remaining tests
  if [ $test_count -lt $total_tests ]; then
    echo "📋 Remaining tests:"
    for ((i=test_count; i<total_tests; i++)); do
      IFS=':' read -r remaining_test_name _ _ _ <<< "${TESTS[$i]}"
      echo "   $((i+1)). $remaining_test_name"
    done
  fi
  echo ""

  # Run test
  if run_single_test "$test_config" "$test_count" "$total_tests"; then
    echo "✅ Test $test_count/$total_tests completed successfully: $test_name"
  else
    echo "❌ Test $test_count/$total_tests failed: $test_name"
  fi

  echo ""
  echo "============================================================================"
  ((test_count++))
done

echo "🎉 All tests completed!"

echo ""
echo "🎉 ALL TESTS COMPLETED! 🎉"
echo "=========================="
echo "📁 Results saved in: $OUTPUT_DIR/"
echo "📊 Performance measurements saved in: $MEASUREMENTS_FILE"
echo ""

# Add summary to measurements file
{
  echo "SUMMARY"
  echo "======="
  echo "Total tests completed: $(($test_count - 1))"
  echo "Successful tests:"
} >> "$MEASUREMENTS_FILE"

# Show results summary
if ls "$OUTPUT_DIR"/*.mp4 >/dev/null 2>&1; then
  echo "📊 Test summary:"
  for result_file in "$OUTPUT_DIR"/*.mp4; do
    if [ -f "$result_file" ]; then
      filename=$(basename "$result_file")
      file_size=$(du -h "$result_file" | cut -f1)
      echo "   ✅ $filename ($file_size)"
      echo "- $filename ($file_size)" >> "$MEASUREMENTS_FILE"
    fi
  done
else
  echo "   ❌ No successful results found"
  echo "- None" >> "$MEASUREMENTS_FILE"
fi

echo "" >> "$MEASUREMENTS_FILE"
echo "For detailed timing data, see individual test results above." >> "$MEASUREMENTS_FILE"

# Cleanup lock file
rm -f "$MEASUREMENTS_FILE.lock"

echo ""
echo "🔍 Compare the results to find the best quality/speed trade-off for your needs!"
echo "📈 Check $MEASUREMENTS_FILE for detailed performance analysis and 2-hour estimates!"
echo "📋 Individual test logs available in $OUTPUT_DIR/test_*.log"
echo "🚀 CUDA acceleration should provide 2-4x speedup over CPU!"
