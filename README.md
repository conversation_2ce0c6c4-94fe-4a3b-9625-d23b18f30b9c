# Video Processing Script Documentation

## Overview

This script (`run2.sh`) downloads video streams from m3u8 URLs, concatenates them, trims to specified time ranges, and applies quality enhancements using RTX 4090 GPU acceleration.

## Features

- Downloads TS segments from m3u8 playlists
- Concatenates multiple video streams
- Time-based trimming with precise timing
- Quality enhancement (720p→1080p, 24fps→30fps)
- RTX 4090 GPU acceleration with NVENC
- Resume functionality for interrupted processing
- Comprehensive timing validation

## Requirements

- Bash shell
- FFmpeg with NVENC support
- NVIDIA GPU (RTX 4090 recommended)
- curl for downloading streams

## Quick Start

1. Create a `source.txt` file with timing parameters and URLs
2. Run the script: `./run2.sh`
3. For interrupted processing: `./run2.sh --resume`

## Configuration File (source.txt)

Create a `source.txt` file in the same directory with the following format:

```
video_start=10:00:00PM
trim_start=10:07:43PM
trim_end=11:15:00PM
https://example.com/stream1.m3u8
https://example.com/stream2.m3u8
https://example.com/stream3.m3u8
# Comments start with #
```

### Timing Parameters

- **video_start**: When the original video recording started
- **trim_start**: When you want the final video to start
- **trim_end**: When you want the final video to end

**Important**: Must follow the order: `video_start < trim_start < trim_end`

### Supported Time Formats

- **12-hour format**: `10:07:43PM`, `11:15:00AM`
- **24-hour format**: `22:07:43`, `11:15:00`

## Usage

### Basic Usage
```bash
./run2.sh
```

### Resume Mode
```bash
./run2.sh --resume
```

### Help
```bash
./run2.sh --help
```

## Script Configuration

Edit the script header to modify settings:

```bash
# Frame rate enhancement options:
TARGET_FPS="30"     # "30", "60", or "60_rife"
FINAL_OUTPUT="final_concatenated.mp4"
```

### Frame Rate Options

- **"30"**: Simple 24fps → 30fps (fast, good quality)
- **"60"**: 24fps → 60fps using FFmpeg interpolation (slower, decent quality)  
- **"60_rife"**: 24fps → 60fps using RIFE AI (slowest, best quality)

## Output

- **Final video**: `final_concatenated.mp4`
- **Temp files**: Archived in `TEMP_YYYYMMDD_HHMMSS/` folder
- **Quality improvements**:
  - Resolution: 720p → 1080p (Lanczos upscaling)
  - Frame rate: 24fps → 30fps (or higher if configured)
  - Bitrate: Enhanced to 8Mbps
  - Audio: Enhanced to 256kbps AAC
  - Color: Enhanced contrast, brightness, saturation

## Resume Functionality

The script automatically detects completed steps:

- ✅ **Downloads**: Skips if `video_N.mp4` files exist
- ✅ **Concatenation list**: Skips if `concat_list.txt` exists  
- ✅ **Concatenation**: Skips if `temp_concatenated.mp4` exists
- ✅ **Final output**: Skips if `final_concatenated.mp4` exists

To force regeneration of a step, delete the corresponding file.

## Error Handling

### Common Issues

1. **Invalid timing order**
   ```
   ❌ ERROR: Invalid timing order - video_start must be before trim_start
   ```
   **Solution**: Check your timing parameters in `source.txt`

2. **Missing timing parameters**
   ```
   ❌ ERROR: Missing timing parameters in source.txt
   ```
   **Solution**: Ensure all three timing parameters are present

3. **Corrupted concatenated file**
   ```
   ❌ ERROR: Failed to apply quality enhancements
   ```
   **Solution**: Delete `temp_concatenated.mp4` and run with `--resume`

### Troubleshooting

1. **Check timing parameters**: Ensure proper order and format
2. **Verify URLs**: Test m3u8 URLs in a browser
3. **Check disk space**: Large video files require significant storage
4. **GPU issues**: Script falls back to software encoding if NVENC fails

## Examples

### Example 1: Basic Usage
```bash
# source.txt
video_start=9:00:00PM
trim_start=9:15:30PM  
trim_end=10:45:00PM
https://stream1.example.com/playlist.m3u8
https://stream2.example.com/playlist.m3u8

# Run script
./run2.sh
```

### Example 2: Resume After Interruption
```bash
# If script was interrupted during processing
./run2.sh --resume
```

### Example 3: Force Regeneration
```bash
# Delete corrupted file and resume
rm TEMP_*/temp_concatenated.mp4
./run2.sh --resume
```

## Performance Notes

- **RTX 4090**: Optimized for maximum performance
- **Memory**: Uses `PYTORCH_CUDA_ALLOC_CONF` for optimal GPU memory management
- **Processing time**: Depends on video length and quality settings
- **Storage**: Keep temp files for debugging (automatically archived)

## Support

For issues or questions:
1. Check the error messages for specific guidance
2. Verify timing parameters and URL accessibility
3. Ensure sufficient disk space and GPU memory
4. Use `--resume` for interrupted processing
