#!/bin/bash

# RIFE Video Interpolation Script - Optimized for RTX 4090
# Usage: ./rife_interpolate.sh input.mp4 [output.mp4]

set -e  # Exit on error
set -o pipefail  # Exit on pipe failures

# ---- CONFIG ----
RIFE_PATH="/mnt/c/Users/<USER>/code/Practical-RIFE"  # WSL2 path to Windows directory
TARGET_FPS=60
TEMP_DIR_PREFIX="rife_temp"
# ----------------

# ---- Performance Optimizations ----
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:2048,expandable_segments:True
export OMP_NUM_THREADS=8  # Optimize CPU threads for frame extraction/encoding
export CUDA_LAUNCH_BLOCKING=0  # Allow async CUDA operations
# ------------------------------------

# ---- Command line argument parsing ----
if [ $# -eq 0 ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  echo "🎬 RIFE Video Interpolation Script"
  echo "=================================="
  echo "Usage: $0 <input.mp4> [output.mp4]"
  echo ""
  echo "Description:"
  echo "  Interpolates video frames using RIFE AI to increase FPS to 60"
  echo "  Maintains original video duration and speed"
  echo "  Uses NVIDIA hardware acceleration throughout"
  echo ""
  echo "Examples:"
  echo "  $0 input.mp4                    # Output: input_60fps.mp4"
  echo "  $0 input.mp4 output.mp4         # Custom output name"
  echo ""
  echo "Requirements:"
  echo "  - NVIDIA GPU with CUDA support"
  echo "  - RIFE installation at: $RIFE_PATH"
  echo "  - FFmpeg with NVENC support"
  exit 0
fi

INPUT_FILE="$1"
if [ $# -ge 2 ]; then
  OUTPUT_FILE="$2"
else
  # Generate output filename
  filename=$(basename "$INPUT_FILE" .mp4)
  OUTPUT_FILE="${filename}_60fps.mp4"
fi

# ---- Validation ----
if [ ! -f "$INPUT_FILE" ]; then
  echo "❌ Error: Input file '$INPUT_FILE' not found"
  exit 1
fi

if [ ! -d "$RIFE_PATH" ]; then
  echo "❌ Error: RIFE not found at '$RIFE_PATH'"
  echo "   Please update RIFE_PATH in the script"
  exit 1
fi

# ---- Setup ----
CURRENT_DIR=$(pwd)
TEMP_DIR="${TEMP_DIR_PREFIX}_$(date +%s)_$$"
mkdir -p "$TEMP_DIR"

echo "🎬 RIFE Video Interpolation (Optimized)"
echo "======================================="
echo "📁 Input: $INPUT_FILE"
echo "📁 Output: $OUTPUT_FILE"
echo "📁 Temp directory: $TEMP_DIR"
echo "🎯 Target FPS: $TARGET_FPS"
echo ""

# Cleanup function
cleanup() {
  echo ""
  echo "🧹 Cleaning up temporary files..."
  cd "$CURRENT_DIR"
  rm -rf "$TEMP_DIR"
  echo "✅ Cleanup completed"
}

# Set trap to cleanup on exit
trap cleanup EXIT

# ---- Get video info ----
echo "🔍 Analyzing input video..."
original_fps=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate -of csv=p=0 "$INPUT_FILE" | bc -l | xargs printf "%.2f")
duration=$(ffprobe -v quiet -show_entries format=duration -of csv=p=0 "$INPUT_FILE" | xargs printf "%.2f")
echo "   Original FPS: $original_fps"
echo "   Duration: ${duration}s"
echo ""

# Start total timing
total_start_time=$(date +%s)

# ---- Step 1: Extract frames ----
echo "⏳ Step 1/4: Extracting frames from video..."
temp_frames="$TEMP_DIR/frames"
mkdir -p "$temp_frames"

extract_start_time=$(date +%s)
ffmpeg -y -hide_banner -loglevel error -i "$INPUT_FILE" \
  -vf "scale=trunc(iw/2)*2:trunc(ih/2)*2" \
  -pix_fmt rgb24 \
  -compression_level 1 \
  -pred mixed \
  "$temp_frames/%07d.png" || {
  echo "❌ Failed to extract frames"
  exit 1
}

frame_count=$(find "$temp_frames" -name "*.png" -type f | wc -l)
extract_end_time=$(date +%s)
extract_duration=$((extract_end_time - extract_start_time))
echo "✅ Extracted $frame_count frames (${extract_duration}s)"
echo ""

# ---- Step 2: RIFE Interpolation ----
echo "⏳ Step 2/4: Running RIFE interpolation..."
echo "   This may take several minutes depending on video length..."

temp_interpolated="$TEMP_DIR/interpolated"
mkdir -p "$temp_interpolated"

# Convert paths for Windows
win_temp_frames=$(echo "$CURRENT_DIR/$temp_frames" | sed 's|/mnt/c|C:|' | sed 's|/|\\|g')

rife_start_time=$(date +%s)
cd "$RIFE_PATH"

# Run RIFE with optimized settings
python.exe inference_video.py \
  --img "$win_temp_frames" \
  --exp 2 \
  --scale 2.0 \
  --fp16 \
  --multi 2 || {
  echo "❌ RIFE interpolation failed"
  cd "$CURRENT_DIR"
  exit 1
}

cd "$CURRENT_DIR"
rife_end_time=$(date +%s)
rife_duration=$((rife_end_time - rife_start_time))

# Move interpolated frames
echo "   📁 Moving interpolated frames..."
if [ -d "$RIFE_PATH/vid_out" ] && [ "$(ls -A "$RIFE_PATH/vid_out" 2>/dev/null)" ]; then
  mv "$RIFE_PATH/vid_out"/*.png "$temp_interpolated/" 2>/dev/null || true
fi

interpolated_count=$(find "$temp_interpolated" -name "*.png" -type f 2>/dev/null | wc -l)
echo "✅ RIFE completed: $frame_count → $interpolated_count frames (${rife_duration}s)"
echo ""

if [ "$interpolated_count" -eq 0 ]; then
  echo "❌ No interpolated frames generated"
  exit 1
fi

# ---- Step 3: Calculate encoding parameters ----
echo "📊 Calculating encoding parameters..."
interpolation_ratio=$(echo "scale=6; $interpolated_count / $frame_count" | bc -l)
input_fps=$(echo "scale=6; $interpolated_count / $duration" | bc -l)
echo "   Interpolation ratio: ${interpolation_ratio}x"
echo "   Input FPS for encoding: ${input_fps} → Output: ${TARGET_FPS} FPS"
echo ""

# ---- Step 4: Encode video ----
echo "⏳ Step 3/4: Encoding video with NVIDIA hardware acceleration..."

encode_start_time=$(date +%s)

# Try optimized NVENC first
if ffmpeg \
  -y \
  -hide_banner \
  -loglevel warning \
  -framerate "$input_fps" \
  -i "$temp_interpolated/%07d.png" \
  -c:v h264_nvenc \
  -preset p4 \
  -rc vbr \
  -cq 20 \
  -qmin 18 \
  -qmax 22 \
  -r "$TARGET_FPS" \
  -pix_fmt yuv420p \
  -movflags +faststart \
  -bf 3 \
  -b_ref_mode middle \
  "$OUTPUT_FILE" 2>/dev/null; then
  echo "✅ Encoded with NVENC (optimized mode)"
elif ffmpeg \
  -y \
  -hide_banner \
  -loglevel warning \
  -framerate "$input_fps" \
  -i "$temp_interpolated/%07d.png" \
  -c:v h264_nvenc \
  -preset fast \
  -b:v 8M \
  -maxrate 12M \
  -bufsize 16M \
  -r "$TARGET_FPS" \
  -pix_fmt yuv420p \
  -movflags +faststart \
  "$OUTPUT_FILE" 2>/dev/null; then
  echo "✅ Encoded with NVENC (fallback mode)"
else
  echo "⚠️  NVENC failed, using software encoding..."
  ffmpeg \
    -y \
    -hide_banner \
    -loglevel error \
    -framerate "$input_fps" \
    -i "$temp_interpolated/%07d.png" \
    -c:v libx264 \
    -preset fast \
    -crf 20 \
    -r "$TARGET_FPS" \
    -pix_fmt yuv420p \
    -movflags +faststart \
    "$OUTPUT_FILE" || {
    echo "❌ Failed to encode video"
    exit 1
  }
fi

encode_end_time=$(date +%s)
encode_duration=$((encode_end_time - encode_start_time))

# ---- Final results ----
total_end_time=$(date +%s)
total_duration=$((total_end_time - total_start_time))

file_size=$(du -h "$OUTPUT_FILE" | cut -f1)
processing_rate=$(echo "scale=4; $duration / $total_duration" | bc -l)

# Convert seconds to readable format
format_time() {
  local seconds=$1
  local minutes=$((seconds / 60))
  local remaining_seconds=$((seconds % 60))
  if [ $minutes -gt 0 ]; then
    echo "${minutes}m ${remaining_seconds}s"
  else
    echo "${seconds}s"
  fi
}

echo ""
echo "🎉 SUCCESS! Video interpolation completed"
echo "========================================"
echo "📁 Output file: $OUTPUT_FILE ($file_size)"
echo "⏱️  Timing breakdown:"
echo "   - Frame extraction: $(format_time $extract_duration)"
echo "   - RIFE interpolation: $(format_time $rife_duration)"
echo "   - Video encoding: $(format_time $encode_duration)"
echo "   - Total processing time: $(format_time $total_duration)"
echo "📊 Processing rate: ${processing_rate}x realtime"
echo "🎬 Frame count: $frame_count → $interpolated_count (${interpolation_ratio}x)"

# Verify output
if [ -f "$OUTPUT_FILE" ]; then
  output_fps=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate -of csv=p=0 "$OUTPUT_FILE" | bc -l | xargs printf "%.2f")
  output_duration=$(ffprobe -v quiet -show_entries format=duration -of csv=p=0 "$OUTPUT_FILE" | xargs printf "%.2f")
  echo "✅ Output verification:"
  echo "   - FPS: $output_fps"
  echo "   - Duration: ${output_duration}s"
fi

echo ""
echo "⏱️  TOTAL PROCESSING TIME: $(format_time $total_duration)"
echo "🚀 Ready for playback!"
